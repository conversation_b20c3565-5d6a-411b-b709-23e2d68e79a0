<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乔哈里视窗 - 自我认知与人际关系工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .intro {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #4CAF50;
        }
        
        .svg-container {
            text-align: center;
            margin: 40px 0;
            background: #fafafa;
            padding: 30px;
            border-radius: 10px;
            box-shadow: inset 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .quadrant-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 40px;
        }
        
        .quadrant {
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .quadrant:hover {
            transform: translateY(-5px);
        }
        
        .open { background: linear-gradient(135deg, #81C784, #66BB6A); color: white; }
        .blind { background: linear-gradient(135deg, #FFB74D, #FFA726); color: white; }
        .hidden { background: linear-gradient(135deg, #64B5F6, #42A5F5); color: white; }
        .unknown { background: linear-gradient(135deg, #E57373, #EF5350); color: white; }
        
        .quadrant h3 {
            margin-top: 0;
            font-size: 1.4em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .benefits {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .benefits h3 {
            color: #2e7d32;
            margin-top: 0;
        }
        
        .benefits ul {
            list-style-type: none;
            padding: 0;
        }
        
        .benefits li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .benefits li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>乔哈里视窗</h1>
            <p>Johari Window - 自我认知与人际关系的心理学工具</p>
        </div>
        
        <div class="content">
            <div class="intro">
                <p><strong>乔哈里视窗</strong>是一个心理学工具，用于帮助人们了解自己与他人之间的关系和互动。它由美国心理学家约瑟夫·卢夫特（Joseph Luft）和哈里·英厄姆（Harry Ingham）在1955年提出，因此得名"乔哈里视窗"。</p>
                <p>该模型将个人的自我认知和他人对自己的认知分为四个区域，帮助我们更好地理解自己和改善人际关系。</p>
            </div>
            
            <div class="svg-container">
                <h3 style="margin-top: 0; color: #333;">乔哈里视窗四象限图</h3>
                <svg width="500" height="400" viewBox="0 0 500 400" style="max-width: 100%; height: auto;">
                    <!-- 背景 -->
                    <rect width="500" height="400" fill="#f8f9fa" stroke="#ddd" stroke-width="2"/>
                    
                    <!-- 象限分割线 -->
                    <line x1="250" y1="50" x2="250" y2="350" stroke="#333" stroke-width="3"/>
                    <line x1="50" y1="200" x2="450" y2="200" stroke="#333" stroke-width="3"/>
                    
                    <!-- 象限背景色 -->
                    <rect x="50" y="50" width="200" height="150" fill="#81C784" opacity="0.8"/>
                    <rect x="250" y="50" width="200" height="150" fill="#FFB74D" opacity="0.8"/>
                    <rect x="50" y="200" width="200" height="150" fill="#64B5F6" opacity="0.8"/>
                    <rect x="250" y="200" width="200" height="150" fill="#E57373" opacity="0.8"/>
                    
                    <!-- 顶部标签 -->
                    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#333">自己知道</text>
                    <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#333">自己不知道</text>
                    
                    <!-- 左侧标签 -->
                    <text x="25" y="125" text-anchor="middle" font-size="16" font-weight="bold" fill="#333" transform="rotate(-90, 25, 125)">他人知道</text>
                    <text x="25" y="275" text-anchor="middle" font-size="16" font-weight="bold" fill="#333" transform="rotate(-90, 25, 275)">他人不知道</text>
                    
                    <!-- 象限标题和内容 -->
                    <!-- 公开区 -->
                    <text x="150" y="80" text-anchor="middle" font-size="18" font-weight="bold" fill="white">公开区</text>
                    <text x="150" y="100" text-anchor="middle" font-size="14" fill="white">Open Area</text>
                    <text x="150" y="130" text-anchor="middle" font-size="12" fill="white">公开的信息</text>
                    <text x="150" y="145" text-anchor="middle" font-size="12" fill="white">兴趣、技能</text>
                    <text x="150" y="160" text-anchor="middle" font-size="12" fill="white">公开行为</text>
                    
                    <!-- 盲区 -->
                    <text x="350" y="80" text-anchor="middle" font-size="18" font-weight="bold" fill="white">盲区</text>
                    <text x="350" y="100" text-anchor="middle" font-size="14" fill="white">Blind Area</text>
                    <text x="350" y="130" text-anchor="middle" font-size="12" fill="white">行为习惯</text>
                    <text x="350" y="145" text-anchor="middle" font-size="12" fill="white">性格特点</text>
                    <text x="350" y="160" text-anchor="middle" font-size="12" fill="white">他人观察到的</text>
                    
                    <!-- 隐藏区 -->
                    <text x="150" y="230" text-anchor="middle" font-size="18" font-weight="bold" fill="white">隐藏区</text>
                    <text x="150" y="250" text-anchor="middle" font-size="14" fill="white">Hidden Area</text>
                    <text x="150" y="280" text-anchor="middle" font-size="12" fill="white">个人秘密</text>
                    <text x="150" y="295" text-anchor="middle" font-size="12" fill="white">隐私</text>
                    <text x="150" y="310" text-anchor="middle" font-size="12" fill="white">内心想法</text>
                    
                    <!-- 未知区 -->
                    <text x="350" y="230" text-anchor="middle" font-size="18" font-weight="bold" fill="white">未知区</text>
                    <text x="350" y="250" text-anchor="middle" font-size="14" fill="white">Unknown Area</text>
                    <text x="350" y="280" text-anchor="middle" font-size="12" fill="white">潜在能力</text>
                    <text x="350" y="295" text-anchor="middle" font-size="12" fill="white">未触及情感</text>
                    <text x="350" y="310" text-anchor="middle" font-size="12" fill="white">未来可能性</text>
                    
                    <!-- 箭头指示改善方向 -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#4CAF50"/>
                        </marker>
                    </defs>
                    
                    <!-- 扩大公开区的箭头 -->
                    <path d="M 180 180 Q 200 160 220 180" stroke="#4CAF50" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                    <path d="M 180 220 Q 200 240 220 220" stroke="#4CAF50" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                    
                    <text x="475" y="380" text-anchor="end" font-size="12" fill="#666">目标：扩大公开区</text>
                </svg>
            </div>
            
            <div class="quadrant-details">
                <div class="quadrant open">
                    <h3>🌟 公开区 (Open Area)</h3>
                    <p><strong>定义：</strong>自己知道、他人也知道的信息</p>
                    <p><strong>包含：</strong>公开的兴趣爱好、已知的技能、公开的行为表现、共同的经历等</p>
                    <p><strong>特点：</strong>这是沟通最顺畅的区域，是建立信任关系的基础</p>
                </div>
                
                <div class="quadrant blind">
                    <h3>👁️ 盲区 (Blind Area)</h3>
                    <p><strong>定义：</strong>自己不知道、但他人知道的信息</p>
                    <p><strong>包含：</strong>无意识的行为习惯、性格特点、说话方式、肢体语言等</p>
                    <p><strong>改善：</strong>通过他人反馈和自我观察来减少盲区</p>
                </div>
                
                <div class="quadrant hidden">
                    <h3>🔒 隐藏区 (Hidden Area)</h3>
                    <p><strong>定义：</strong>自己知道、但他人不知道的信息</p>
                    <p><strong>包含：</strong>个人秘密、私人想法、过往经历、内心感受等</p>
                    <p><strong>策略：</strong>适当的自我披露可以增进关系，但需要把握分寸</p>
                </div>
                
                <div class="quadrant unknown">
                    <h3>❓ 未知区 (Unknown Area)</h3>
                    <p><strong>定义：</strong>自己和他人都不知道的信息</p>
                    <p><strong>包含：</strong>潜在能力、未开发的才能、深层的情感、未来的可能性</p>
                    <p><strong>探索：</strong>通过新体验、挑战和深度反思来发现</p>
                </div>
            </div>
            
            <div class="benefits">
                <h3>🎯 乔哈里视窗的应用价值</h3>
                <ul>
                    <li>提高自我认知能力，更好地了解自己的优势和盲点</li>
                    <li>改善人际关系，增进相互理解和信任</li>
                    <li>促进团队合作，提高团队沟通效率</li>
                    <li>指导个人成长，明确自我发展方向</li>
                    <li>增强领导力，提高管理和沟通技巧</li>
                    <li>减少误解和冲突，建立更和谐的关系</li>
                </ul>
                
                <p><strong>使用建议：</strong>通过自我反思和与他人互动，努力扩大"公开区"，减少"盲区"和"隐藏区"，同时保持对"未知区"的探索精神。</p>
            </div>
        </div>
        
        <div class="footer">
            <p>乔哈里视窗 - 由约瑟夫·卢夫特和哈里·英厄姆于1955年提出</p>
        </div>
    </div>
</body>
</html>
